import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { ilike, or, desc, count, eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const includeResults = searchParams.get('includeResults') === 'true';

    const offset = (page - 1) * limit;

    // Build search conditions
    const searchConditions = search
      ? or(
          ilike(candidates.fullName, `%${search}%`),
          ilike(candidates.email, `%${search}%`),
          ilike(candidates.passportNumber, `%${search}%`),
          ilike(candidates.candidateNumber, `%${search}%`)
        )
      : undefined;

    if (includeResults) {
      // Get candidates with their test results
      const candidatesWithResults = await db
        .select({
          id: candidates.id,
          candidateNumber: candidates.candidateNumber,
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          dateOfBirth: candidates.dateOfBirth,
          nationality: candidates.nationality,
          passportNumber: candidates.passportNumber,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
          photoUrl: candidates.photoUrl,
          createdAt: candidates.createdAt,
          updatedAt: candidates.updatedAt,
          resultId: testResults.id,
          listeningBandScore: testResults.listeningBandScore,
          readingBandScore: testResults.readingBandScore,
          writingBandScore: testResults.writingBandScore,
          speakingBandScore: testResults.speakingBandScore,
          overallBandScore: testResults.overallBandScore,
          status: testResults.status,
        })
        .from(candidates)
        .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
        .where(searchConditions)
        .orderBy(desc(candidates.createdAt))
        .limit(limit)
        .offset(offset);

      // Transform the data to include hasResult flag and nested result object
      const transformedCandidates = candidatesWithResults.map(candidate => ({
        id: candidate.id,
        candidateNumber: candidate.candidateNumber,
        fullName: candidate.fullName,
        email: candidate.email,
        phoneNumber: candidate.phoneNumber,
        dateOfBirth: candidate.dateOfBirth,
        nationality: candidate.nationality,
        passportNumber: candidate.passportNumber,
        testDate: candidate.testDate,
        testCenter: candidate.testCenter,
        photoUrl: candidate.photoUrl,
        createdAt: candidate.createdAt,
        updatedAt: candidate.updatedAt,
        hasResult: !!candidate.resultId,
        result: candidate.resultId ? {
          id: candidate.resultId,
          listeningBandScore: candidate.listeningBandScore,
          readingBandScore: candidate.readingBandScore,
          writingBandScore: candidate.writingBandScore,
          speakingBandScore: candidate.speakingBandScore,
          overallBandScore: candidate.overallBandScore,
          status: candidate.status,
        } : null,
      }));

      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(candidates)
        .where(searchConditions);

      const total = totalResult[0]?.count || 0;

      return NextResponse.json({
        candidates: transformedCandidates,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    } else {
      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(candidates)
        .where(searchConditions);

      const total = totalResult[0]?.count || 0;

      // Get candidates
      const candidatesList = await db
        .select()
        .from(candidates)
        .where(searchConditions)
        .orderBy(desc(candidates.createdAt))
        .limit(limit)
        .offset(offset);

      return NextResponse.json({
        candidates: candidatesList,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    }
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    const requiredFields = [
      'fullName',
      'email',
      'phoneNumber',
      'dateOfBirth',
      'nationality',
      'passportNumber',
      'testDate',
      'testCenter'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Check if this is an existing candidate registering for a new test
    const testDate = new Date(data.testDate);

    // Check for existing registration for the same test date
    const existingRegistration = await db
      .select()
      .from(candidates)
      .where(
        or(
          eq(candidates.email, data.email),
          eq(candidates.passportNumber, data.passportNumber)
        )
      );

    // Check if candidate already registered for this specific test date
    const duplicateRegistration = existingRegistration.find(candidate =>
      candidate.testDate.getTime() === testDate.getTime()
    );

    if (duplicateRegistration) {
      return NextResponse.json(
        { error: 'Candidate is already registered for this test date' },
        { status: 409 }
      );
    }

    // If existing candidate found, use their existing candidate number and basic info
    let candidateNumber = data.candidateNumber;
    let candidateData = {
      fullName: data.fullName,
      email: data.email,
      phoneNumber: data.phoneNumber,
      dateOfBirth: new Date(data.dateOfBirth),
      nationality: data.nationality,
      passportNumber: data.passportNumber,
      testDate: testDate,
      testCenter: data.testCenter,
      photoUrl: data.photoUrl,
      photoData: data.photoData,
    };

    const existingCandidate = existingRegistration.find(candidate =>
      candidate.email === data.email || candidate.passportNumber === data.passportNumber
    );

    if (existingCandidate) {
      // Existing candidate registering for new test - reuse their candidate number
      candidateNumber = existingCandidate.candidateNumber;

      // Use existing candidate's core information but allow updates for contact info and photo
      candidateData = {
        ...candidateData,
        fullName: existingCandidate.fullName, // Keep original name
        dateOfBirth: existingCandidate.dateOfBirth, // Keep original DOB
        nationality: existingCandidate.nationality, // Keep original nationality
        // Allow updates to contact info and photo for new registration
        email: data.email,
        phoneNumber: data.phoneNumber,
        photoUrl: data.photoUrl || existingCandidate.photoUrl,
        photoData: data.photoData || existingCandidate.photoData,
      };
    } else {
      // New candidate - generate new candidate number
      if (!candidateNumber) {
        // Get the count of existing candidates to generate next number
        const candidateCount = await db
          .select({ count: candidates.id })
          .from(candidates);

        const nextNumber = candidateCount.length + 1;
        candidateNumber = nextNumber.toString().padStart(3, '0');
      }
    }

    // Create new test registration
    const newCandidate = await db
      .insert(candidates)
      .values({
        candidateNumber,
        ...candidateData,
      })
      .returning();

    return NextResponse.json(newCandidate[0], { status: 201 });
  } catch (error) {
    console.error('Error creating candidate:', error);

    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      if (error.message.includes('email') && error.message.includes('test_date')) {
        return NextResponse.json(
          { error: 'Candidate is already registered for this test date' },
          { status: 409 }
        );
      }
      if (error.message.includes('passport') && error.message.includes('test_date')) {
        return NextResponse.json(
          { error: 'Candidate with this passport number is already registered for this test date' },
          { status: 409 }
        );
      }
      if (error.message.includes('candidate_number')) {
        return NextResponse.json(
          { error: 'Candidate number already exists' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
