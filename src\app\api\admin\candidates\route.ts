import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { ilike, or, desc, count, eq, and } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const includeResults = searchParams.get('includeResults') === 'true';

    const offset = (page - 1) * limit;

    // Build search conditions for candidates
    const candidateSearchConditions = search
      ? or(
          ilike(candidates.fullName, `%${search}%`),
          ilike(candidates.email, `%${search}%`),
          ilike(candidates.passportNumber, `%${search}%`)
        )
      : undefined;

    // Build search conditions for test registrations
    const registrationSearchConditions = search
      ? ilike(testRegistrations.candidateNumber, `%${search}%`)
      : undefined;

    if (includeResults) {
      // Get test registrations with candidate info and test results
      const registrationsWithResults = await db
        .select({
          // Test registration info
          registrationId: testRegistrations.id,
          candidateNumber: testRegistrations.candidateNumber,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
          registrationStatus: testRegistrations.status,
          registrationCreatedAt: testRegistrations.createdAt,

          // Candidate info
          candidateId: candidates.id,
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          dateOfBirth: candidates.dateOfBirth,
          nationality: candidates.nationality,
          passportNumber: candidates.passportNumber,
          photoUrl: candidates.photoUrl,
          candidateCreatedAt: candidates.createdAt,
          candidateUpdatedAt: candidates.updatedAt,

          // Test result info
          resultId: testResults.id,
          listeningBandScore: testResults.listeningBandScore,
          readingBandScore: testResults.readingBandScore,
          writingBandScore: testResults.writingBandScore,
          speakingBandScore: testResults.speakingBandScore,
          overallBandScore: testResults.overallBandScore,
          resultStatus: testResults.status,
        })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .leftJoin(testResults, eq(testRegistrations.id, testResults.testRegistrationId))
        .where(
          search
            ? or(candidateSearchConditions, registrationSearchConditions)
            : undefined
        )
        .orderBy(desc(testRegistrations.createdAt))
        .limit(limit)
        .offset(offset);

      // Transform the data to match the expected format
      const transformedRegistrations = registrationsWithResults.map(reg => ({
        // Use registration ID as the primary ID for this view
        id: reg.registrationId,
        candidateId: reg.candidateId,
        candidateNumber: reg.candidateNumber,
        fullName: reg.fullName,
        email: reg.email,
        phoneNumber: reg.phoneNumber,
        dateOfBirth: reg.dateOfBirth,
        nationality: reg.nationality,
        passportNumber: reg.passportNumber,
        testDate: reg.testDate,
        testCenter: reg.testCenter,
        photoUrl: reg.photoUrl,
        createdAt: reg.registrationCreatedAt,
        updatedAt: reg.candidateUpdatedAt,
        hasResult: !!reg.resultId,
        result: reg.resultId ? {
          id: reg.resultId,
          listeningBandScore: reg.listeningBandScore,
          readingBandScore: reg.readingBandScore,
          writingBandScore: reg.writingBandScore,
          speakingBandScore: reg.speakingBandScore,
          overallBandScore: reg.overallBandScore,
          status: reg.resultStatus,
        } : null,
      }));

      // Get total count of test registrations
      const totalResult = await db
        .select({ count: count() })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(
          search
            ? or(candidateSearchConditions, registrationSearchConditions)
            : undefined
        );

      const total = totalResult[0]?.count || 0;

      return NextResponse.json({
        candidates: transformedRegistrations,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    } else {
      // Get total count of test registrations
      const totalResult = await db
        .select({ count: count() })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(
          search
            ? or(candidateSearchConditions, registrationSearchConditions)
            : undefined
        );

      const total = totalResult[0]?.count || 0;

      // Get test registrations with candidate info
      const registrationsList = await db
        .select({
          id: testRegistrations.id,
          candidateId: candidates.id,
          candidateNumber: testRegistrations.candidateNumber,
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          dateOfBirth: candidates.dateOfBirth,
          nationality: candidates.nationality,
          passportNumber: candidates.passportNumber,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
          photoUrl: candidates.photoUrl,
          createdAt: testRegistrations.createdAt,
          updatedAt: candidates.updatedAt,
        })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(
          search
            ? or(candidateSearchConditions, registrationSearchConditions)
            : undefined
        )
        .orderBy(desc(testRegistrations.createdAt))
        .limit(limit)
        .offset(offset);

      return NextResponse.json({
        candidates: registrationsList,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    }
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    const requiredFields = [
      'fullName',
      'email',
      'phoneNumber',
      'dateOfBirth',
      'nationality',
      'passportNumber',
      'testDate',
      'testCenter'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    const testDate = new Date(data.testDate);

    // Check if candidate already exists
    const existingCandidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.passportNumber, data.passportNumber))
      .limit(1);

    let candidateId: string;
    let isExistingCandidate = false;

    if (existingCandidate.length > 0) {
      // Existing candidate found
      candidateId = existingCandidate[0].id;
      isExistingCandidate = true;

      // Check if already registered for this test date
      const existingRegistration = await db
        .select()
        .from(testRegistrations)
        .where(
          and(
            eq(testRegistrations.candidateId, candidateId),
            eq(testRegistrations.testDate, testDate)
          )
        )
        .limit(1);

      if (existingRegistration.length > 0) {
        return NextResponse.json(
          { error: 'Candidate is already registered for this test date' },
          { status: 409 }
        );
      }
    } else {
      // Create new candidate profile
      const newCandidate = await db
        .insert(candidates)
        .values({
          id: createId(),
          fullName: data.fullName,
          email: data.email,
          phoneNumber: data.phoneNumber,
          dateOfBirth: new Date(data.dateOfBirth),
          nationality: data.nationality,
          passportNumber: data.passportNumber,
          photoUrl: data.photoUrl,
          photoData: data.photoData,
        })
        .returning();

      candidateId = newCandidate[0].id;
    }

    // Generate candidate number scoped to the specific test date
    let candidateNumber = data.candidateNumber;
    if (!candidateNumber) {
      // Get the count of existing registrations for this specific test date
      const registrationsForTestDate = await db
        .select({ count: count() })
        .from(testRegistrations)
        .where(eq(testRegistrations.testDate, testDate));

      const nextNumber = (registrationsForTestDate[0]?.count || 0) + 1;
      candidateNumber = nextNumber.toString().padStart(3, '0');
    }

    // Create test registration
    const newRegistration = await db
      .insert(testRegistrations)
      .values({
        id: createId(),
        candidateId: candidateId,
        candidateNumber: candidateNumber,
        testDate: testDate,
        testCenter: data.testCenter,
        status: 'registered',
      })
      .returning();

    // Get the complete registration with candidate info for response
    const registrationWithCandidate = await db
      .select({
        id: testRegistrations.id,
        candidateId: candidates.id,
        candidateNumber: testRegistrations.candidateNumber,
        fullName: candidates.fullName,
        email: candidates.email,
        phoneNumber: candidates.phoneNumber,
        dateOfBirth: candidates.dateOfBirth,
        nationality: candidates.nationality,
        passportNumber: candidates.passportNumber,
        testDate: testRegistrations.testDate,
        testCenter: testRegistrations.testCenter,
        photoUrl: candidates.photoUrl,
        photoData: candidates.photoData,
        createdAt: testRegistrations.createdAt,
        updatedAt: candidates.updatedAt,
      })
      .from(testRegistrations)
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testRegistrations.id, newRegistration[0].id))
      .limit(1);

    return NextResponse.json({
      ...registrationWithCandidate[0],
      isExistingCandidate
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating candidate:', error);

    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      if (error.message.includes('passport_number')) {
        return NextResponse.json(
          { error: 'A candidate with this passport number already exists' },
          { status: 409 }
        );
      }
      if (error.message.includes('candidate_test_date')) {
        return NextResponse.json(
          { error: 'Candidate is already registered for this test date' },
          { status: 409 }
        );
      }
      if (error.message.includes('candidate_number_test_date')) {
        return NextResponse.json(
          { error: 'Candidate number already exists for this test date' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
