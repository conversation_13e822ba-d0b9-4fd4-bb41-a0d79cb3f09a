import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations, aiFeedback } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { generateAIFeedback } from '@/lib/ai-service';
import { generateCertificate } from '@/lib/certificate-generator';
import { generateCertificateSerial } from '@/lib/utils/certificate';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: resultId } = await params;

    // Get test result with candidate info
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    const { testResult, candidate } = result[0];

    // Check if all required scores are available
    if (!testResult.overallBandScore) {
      return NextResponse.json(
        { error: 'Overall band score is required to complete the result' },
        { status: 400 }
      );
    }

    const results = {
      aiFeedback: null as { id: string } | null,
      certificate: null as { serial: string; downloadUrl: string } | null,
      errors: [] as string[]
    };

    // Generate AI Feedback if not already generated
    if (!testResult.aiFeedbackGenerated) {
      try {
        const feedbackData = await generateAIFeedback(testResult, candidate);

        // Save the feedback to database
        const newFeedback = await db
          .insert(aiFeedback)
          .values({
            testResultId: resultId,
            listeningFeedback: feedbackData.listeningFeedback,
            readingFeedback: feedbackData.readingFeedback,
            writingFeedback: feedbackData.writingFeedback,
            speakingFeedback: feedbackData.speakingFeedback,
            overallFeedback: feedbackData.overallFeedback,
            studyRecommendations: feedbackData.recommendations,
            strengths: feedbackData.strengths,
            weaknesses: feedbackData.weaknesses,
            studyPlan: feedbackData.studyPlan,
          })
          .returning();

        results.aiFeedback = newFeedback[0];
      } catch (error) {
        console.error('Error generating AI feedback:', error);
        results.errors.push('Failed to generate AI feedback');
      }
    }

    // Generate Certificate if not already generated
    if (!testResult.certificateGenerated) {
      try {
        // Generate certificate serial if not exists
        let certificateSerial = testResult.certificateSerial;
        if (!certificateSerial) {
          certificateSerial = generateCertificateSerial();
        }

        // Generate the certificate PDF (this validates the generation process)
        await generateCertificate(testResult, candidate);

        results.certificate = {
          serial: certificateSerial,
          downloadUrl: `/api/certificate/${resultId}`,
        };
      } catch (error) {
        console.error('Error generating certificate:', error);
        results.errors.push('Failed to generate certificate');
      }
    }

    // Update the test result to mark as completed and update flags
    const updateData: Record<string, unknown> = {
      status: 'completed',
      updatedAt: new Date()
    };

    if (results.aiFeedback) {
      updateData.aiFeedbackGenerated = true;
    }

    if (results.certificate) {
      updateData.certificateGenerated = true;
      updateData.certificateSerial = results.certificate.serial;
    }

    await db
      .update(testResults)
      .set(updateData)
      .where(eq(testResults.id, resultId));

    return NextResponse.json({
      success: true,
      message: 'Result completed successfully',
      resultId,
      aiFeedback: results.aiFeedback ? 'Generated' : 'Already exists or failed',
      certificate: results.certificate ? 'Generated' : 'Already exists or failed',
      errors: results.errors,
      downloadUrl: results.certificate?.downloadUrl,
    });

  } catch (error) {
    console.error('Error completing result:', error);
    return NextResponse.json(
      { error: 'Failed to complete result. Please try again.' },
      { status: 500 }
    );
  }
}
