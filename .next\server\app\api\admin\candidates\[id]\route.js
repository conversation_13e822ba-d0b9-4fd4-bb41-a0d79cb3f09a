/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/candidates/[id]/route";
exports.ids = ["app/api/admin/candidates/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/candidates/[id]/route.ts */ \"(rsc)/./src/app/api/admin/candidates/[id]/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_id_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/candidates/[id]/route\",\n        pathname: \"/api/admin/candidates/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/candidates/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\candidates\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/candidates/[id]/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/admin/candidates/[id]/route.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { id: candidateId } = await params;\n        // Get candidate details\n        const candidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, candidateId)).limit(1);\n        if (!candidate.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate not found'\n            }, {\n                status: 404\n            });\n        }\n        // Get all test results for this candidate\n        const candidateTestResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, candidateId)).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt));\n        // Combine candidate data with test results\n        const candidateWithResults = {\n            ...candidate[0],\n            testResults: candidateTestResults\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(candidateWithResults);\n    } catch (error) {\n        console.error('Error fetching candidate details:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { id: candidateId } = await params;\n        const data = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'fullName',\n            'email',\n            'phoneNumber',\n            'dateOfBirth',\n            'nationality',\n            'passportNumber',\n            'testDate',\n            'testCenter'\n        ];\n        for (const field of requiredFields){\n            if (!data[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `${field} is required`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Check if candidate exists\n        const existingCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, candidateId)).limit(1);\n        if (!existingCandidate.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate not found'\n            }, {\n                status: 404\n            });\n        }\n        // Update candidate\n        const updatedCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).set({\n            fullName: data.fullName,\n            email: data.email,\n            phoneNumber: data.phoneNumber,\n            dateOfBirth: data.dateOfBirth,\n            nationality: data.nationality,\n            passportNumber: data.passportNumber,\n            testDate: data.testDate,\n            testCenter: data.testCenter,\n            photoData: data.photoData || null,\n            photoUrl: data.photoUrl || null,\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, candidateId)).returning();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedCandidate[0]);\n    } catch (error) {\n        console.error('Error updating candidate:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { id: candidateId } = await params;\n        // Check if candidate exists\n        const existingCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, candidateId)).limit(1);\n        if (!existingCandidate.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate not found'\n            }, {\n                status: 404\n            });\n        }\n        // Check if candidate has test results\n        const candidateResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, candidateId)).limit(1);\n        if (candidateResults.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Cannot delete candidate with existing test results'\n            }, {\n                status: 400\n            });\n        }\n        // Delete candidate\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.delete(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, candidateId));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Candidate deleted successfully'\n        });\n    } catch (error) {\n        console.error('Error deleting candidate:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/candidates/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        // Composite unique constraint: same candidate cannot register for the same test date\n        uniqueCandidateTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.email, table.testDate),\n        uniquePassportTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.passportNumber, table.testDate)\n    }));\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/@noble","vendor-chunks/@paralleldrive","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();