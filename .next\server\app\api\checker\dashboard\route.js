/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/checker/dashboard/route";
exports.ids = ["app/api/checker/dashboard/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fdashboard%2Froute&page=%2Fapi%2Fchecker%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fdashboard%2Froute&page=%2Fapi%2Fchecker%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/checker/dashboard/route.ts */ \"(rsc)/./src/app/api/checker/dashboard/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/checker/dashboard/route\",\n        pathname: \"/api/checker/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/checker/dashboard/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\checker\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fdashboard%2Froute&page=%2Fapi%2Fchecker%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/checker/dashboard/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/checker/dashboard/route.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function GET() {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const userId = session.user?.id;\n        // Get total results entered by this checker\n        const totalResultsResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId));\n        const totalResultsEntered = totalResultsResult[0]?.count || 0;\n        // Get pending results entered by this checker\n        const pendingResultsResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status, 'pending')));\n        const pendingResults = pendingResultsResult[0]?.count || 0;\n        // Get completed results entered by this checker\n        const completedResultsResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status, 'completed')));\n        const completedResults = completedResultsResult[0]?.count || 0;\n        // Get recent results entered by this checker\n        const recentResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n            overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n            status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status,\n            createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt,\n            candidate: {\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName\n            }\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId)).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt)).limit(10);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            totalResultsEntered,\n            pendingResults,\n            completedResults,\n            recentResults\n        });\n    } catch (error) {\n        console.error('Checker dashboard stats error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/checker/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testRegistrations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrations),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNsQjtBQUNHO0FBRW5DLE1BQU1HLG1CQUFtQkMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBRWpELHNFQUFzRTtBQUN0RSxNQUFNQyxTQUFTTixvREFBUUEsQ0FBQ0Usa0JBQWtCO0lBQUVLLFNBQVM7QUFBTTtBQUNwRCxNQUFNQyxLQUFLVCxnRUFBT0EsQ0FBQ08sUUFBUTtJQUFFTCxNQUFNQSxzQ0FBQUE7QUFBQyxHQUFHO0FBRXJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXGNvZGVzXFxJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbVxcc3JjXFxsaWJcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkcml6emxlIH0gZnJvbSAnZHJpenpsZS1vcm0vcG9zdGdyZXMtanMnO1xuaW1wb3J0IHBvc3RncmVzIGZyb20gJ3Bvc3RncmVzJztcbmltcG9ydCAqIGFzIHNjaGVtYSBmcm9tICcuL3NjaGVtYSc7XG5cbmNvbnN0IGNvbm5lY3Rpb25TdHJpbmcgPSBwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkwhO1xuXG4vLyBEaXNhYmxlIHByZWZldGNoIGFzIGl0IGlzIG5vdCBzdXBwb3J0ZWQgZm9yIFwiVHJhbnNhY3Rpb25cIiBwb29sIG1vZGVcbmNvbnN0IGNsaWVudCA9IHBvc3RncmVzKGNvbm5lY3Rpb25TdHJpbmcsIHsgcHJlcGFyZTogZmFsc2UgfSk7XG5leHBvcnQgY29uc3QgZGIgPSBkcml6emxlKGNsaWVudCwgeyBzY2hlbWEgfSk7XG5cbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hJztcbiJdLCJuYW1lcyI6WyJkcml6emxlIiwicG9zdGdyZXMiLCJzY2hlbWEiLCJjb25uZWN0aW9uU3RyaW5nIiwicHJvY2VzcyIsImVudiIsIkRBVEFCQVNFX1VSTCIsImNsaWVudCIsInByZXBhcmUiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testRegistrations: () => (/* binding */ testRegistrations),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table - Core candidate profile information\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test registrations table - Individual test registrations for candidates\nconst testRegistrations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_registrations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'registered',\n            'completed',\n            'cancelled'\n        ]\n    }).notNull().default('registered'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        // Composite unique constraint: same candidate cannot register for the same test date\n        uniqueCandidateTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.candidateId, table.testDate),\n        // Candidate number should be unique only within each test date\n        uniqueCandidateNumberTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.candidateNumber, table.testDate)\n    }));\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testRegistrationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_registration_id').notNull().references(()=>testRegistrations.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fdashboard%2Froute&page=%2Fapi%2Fchecker%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();