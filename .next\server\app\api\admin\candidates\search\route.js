/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/candidates/search/route";
exports.ids = ["app/api/admin/candidates/search/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/candidates/search/route.ts */ \"(rsc)/./src/app/api/admin/candidates/search/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_search_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/candidates/search/route\",\n        pathname: \"/api/admin/candidates/search\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/candidates/search/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\candidates\\\\search\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRmNhbmRpZGF0ZXMlMkZzZWFyY2glMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFkbWluJTJGY2FuZGlkYXRlcyUyRnNlYXJjaCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFkbWluJTJGY2FuZGlkYXRlcyUyRnNlYXJjaCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNXaW5kb3dzJTIwMTElNUNEZXNrdG9wJTVDY29kZXMlNUNJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDV2luZG93cyUyMDExJTVDRGVza3RvcCU1Q2NvZGVzJTVDSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW0maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ3FFO0FBQ2xKO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRixxQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxXaW5kb3dzIDExXFxcXERlc2t0b3BcXFxcY29kZXNcXFxcSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYWRtaW5cXFxcY2FuZGlkYXRlc1xcXFxzZWFyY2hcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FkbWluL2NhbmRpZGF0ZXMvc2VhcmNoL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYWRtaW4vY2FuZGlkYXRlcy9zZWFyY2hcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2FkbWluL2NhbmRpZGF0ZXMvc2VhcmNoL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEZXNrdG9wXFxcXGNvZGVzXFxcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGFkbWluXFxcXGNhbmRpZGF0ZXNcXFxcc2VhcmNoXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/candidates/search/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/admin/candidates/search/route.ts ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { query } = await request.json();\n        if (!query) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Search query is required'\n            }, {\n                status: 400\n            });\n        }\n        // Search for existing candidates by email or passport number (which stores both passport and birth certificate numbers)\n        const searchCondition = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, query), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, query));\n        // Get candidates matching the email or identification number\n        const existingCandidates = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n            candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.candidateNumber,\n            fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n            email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n            phoneNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.phoneNumber,\n            dateOfBirth: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.dateOfBirth,\n            nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n            passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n            testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testDate,\n            testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter,\n            photoUrl: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoUrl,\n            photoData: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoData\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchCondition).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName);\n        // Group candidates by their core identity (email/passport) to show unique candidates\n        const candidateGroups = new Map();\n        existingCandidates.forEach((candidate)=>{\n            const key = `${candidate.email}-${candidate.passportNumber}`;\n            if (!candidateGroups.has(key)) {\n                candidateGroups.set(key, {\n                    ...candidate,\n                    testRegistrations: []\n                });\n            }\n            candidateGroups.get(key).testRegistrations.push({\n                id: candidate.id,\n                testDate: candidate.testDate,\n                testCenter: candidate.testCenter\n            });\n        });\n        const uniqueCandidates = Array.from(candidateGroups.values());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(uniqueCandidates);\n    } catch (error) {\n        console.error('Candidate search error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/candidates/search/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        // Composite unique constraint: same candidate cannot register for the same test date\n        uniqueCandidateTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.email, table.testDate),\n        uniquePassportTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.passportNumber, table.testDate)\n    }));\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Fsearch%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();