import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { eq, or } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { query } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Search for existing candidates by email or passport number (which stores both passport and birth certificate numbers)
    const searchCondition = or(
      eq(candidates.email, query),
      eq(candidates.passportNumber, query)
    );

    // Get candidates matching the email or identification number
    const existingCandidates = await db
      .select({
        id: candidates.id,
        candidateNumber: candidates.candidateNumber,
        fullName: candidates.fullName,
        email: candidates.email,
        phoneNumber: candidates.phoneNumber,
        dateOfBirth: candidates.dateOfBirth,
        nationality: candidates.nationality,
        passportNumber: candidates.passportNumber,
        testDate: candidates.testDate,
        testCenter: candidates.testCenter,
        photoUrl: candidates.photoUrl,
        photoData: candidates.photoData,
      })
      .from(candidates)
      .where(searchCondition)
      .orderBy(candidates.fullName);

    // Group candidates by their core identity (email/passport) to show unique candidates
    const candidateGroups = new Map();

    existingCandidates.forEach(candidate => {
      const key = `${candidate.email}-${candidate.passportNumber}`;
      if (!candidateGroups.has(key)) {
        candidateGroups.set(key, {
          ...candidate,
          testRegistrations: []
        });
      }
      candidateGroups.get(key).testRegistrations.push({
        id: candidate.id,
        testDate: candidate.testDate,
        testCenter: candidate.testCenter
      });
    });

    const uniqueCandidates = Array.from(candidateGroups.values());

    return NextResponse.json(uniqueCandidates);
  } catch (error) {
    console.error('Candidate search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
