/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/candidates/route";
exports.ids = ["app/api/admin/candidates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/candidates/route.ts */ \"(rsc)/./src/app/api/admin/candidates/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/candidates/route\",\n        pathname: \"/api/admin/candidates\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/candidates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\candidates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/candidates/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/admin/candidates/route.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const search = searchParams.get('search') || '';\n        const includeResults = searchParams.get('includeResults') === 'true';\n        const offset = (page - 1) * limit;\n        // Build search conditions for candidates\n        const candidateSearchConditions = search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, `%${search}%`)) : undefined;\n        // Build search conditions for test registrations\n        const registrationSearchConditions = search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateNumber, `%${search}%`) : undefined;\n        if (includeResults) {\n            // Get test registrations with candidate info and test results\n            const registrationsWithResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                // Test registration info\n                registrationId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id,\n                candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateNumber,\n                testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testDate,\n                testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testCenter,\n                registrationStatus: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.status,\n                registrationCreatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.createdAt,\n                // Candidate info\n                candidateId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n                phoneNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.phoneNumber,\n                dateOfBirth: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.dateOfBirth,\n                nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n                passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n                photoUrl: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoUrl,\n                candidateCreatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt,\n                candidateUpdatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.updatedAt,\n                // Test result info\n                resultId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n                listeningBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.listeningBandScore,\n                readingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.readingBandScore,\n                writingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.writingBandScore,\n                speakingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.speakingBandScore,\n                overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n                resultStatus: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.testRegistrationId)).where(search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.or)(candidateSearchConditions, registrationSearchConditions) : undefined).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.createdAt)).limit(limit).offset(offset);\n            // Transform the data to match the expected format\n            const transformedRegistrations = registrationsWithResults.map((reg)=>({\n                    // Use registration ID as the primary ID for this view\n                    id: reg.registrationId,\n                    candidateId: reg.candidateId,\n                    candidateNumber: reg.candidateNumber,\n                    fullName: reg.fullName,\n                    email: reg.email,\n                    phoneNumber: reg.phoneNumber,\n                    dateOfBirth: reg.dateOfBirth,\n                    nationality: reg.nationality,\n                    passportNumber: reg.passportNumber,\n                    testDate: reg.testDate,\n                    testCenter: reg.testCenter,\n                    photoUrl: reg.photoUrl,\n                    createdAt: reg.registrationCreatedAt,\n                    updatedAt: reg.candidateUpdatedAt,\n                    hasResult: !!reg.resultId,\n                    result: reg.resultId ? {\n                        id: reg.resultId,\n                        listeningBandScore: reg.listeningBandScore,\n                        readingBandScore: reg.readingBandScore,\n                        writingBandScore: reg.writingBandScore,\n                        speakingBandScore: reg.speakingBandScore,\n                        overallBandScore: reg.overallBandScore,\n                        status: reg.resultStatus\n                    } : null\n                }));\n            // Get total count of test registrations\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where(search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.or)(candidateSearchConditions, registrationSearchConditions) : undefined);\n            const total = totalResult[0]?.count || 0;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                candidates: transformedRegistrations,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        } else {\n            // Get total count of test registrations\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where(search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.or)(candidateSearchConditions, registrationSearchConditions) : undefined);\n            const total = totalResult[0]?.count || 0;\n            // Get test registrations with candidate info\n            const registrationsList = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id,\n                candidateId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n                candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateNumber,\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n                phoneNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.phoneNumber,\n                dateOfBirth: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.dateOfBirth,\n                nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n                passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n                testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testDate,\n                testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testCenter,\n                photoUrl: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoUrl,\n                createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.createdAt,\n                updatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.updatedAt\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where(search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.or)(candidateSearchConditions, registrationSearchConditions) : undefined).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.createdAt)).limit(limit).offset(offset);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                candidates: registrationsList,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching candidates:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'fullName',\n            'email',\n            'phoneNumber',\n            'dateOfBirth',\n            'nationality',\n            'passportNumber',\n            'testDate',\n            'testCenter'\n        ];\n        for (const field of requiredFields){\n            if (!data[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `${field} is required`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        const testDate = new Date(data.testDate);\n        // Check if candidate already exists\n        const existingCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, data.passportNumber)).limit(1);\n        let candidateId;\n        let isExistingCandidate = false;\n        if (existingCandidate.length > 0) {\n            // Existing candidate found\n            candidateId = existingCandidate[0].id;\n            isExistingCandidate = true;\n            // Check if already registered for this test date\n            const existingRegistration = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, candidateId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testDate, testDate))).limit(1);\n            if (existingRegistration.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Candidate is already registered for this test date'\n                }, {\n                    status: 409\n                });\n            }\n        } else {\n            // Create new candidate profile\n            const newCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).values({\n                id: (0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_4__.createId)(),\n                fullName: data.fullName,\n                email: data.email,\n                phoneNumber: data.phoneNumber,\n                dateOfBirth: new Date(data.dateOfBirth),\n                nationality: data.nationality,\n                passportNumber: data.passportNumber,\n                photoUrl: data.photoUrl,\n                photoData: data.photoData\n            }).returning();\n            candidateId = newCandidate[0].id;\n        }\n        // Generate candidate number scoped to the specific test date\n        let candidateNumber = data.candidateNumber;\n        if (!candidateNumber) {\n            // Get the count of existing registrations for this specific test date\n            const registrationsForTestDate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testDate, testDate));\n            const nextNumber = (registrationsForTestDate[0]?.count || 0) + 1;\n            candidateNumber = nextNumber.toString().padStart(3, '0');\n        }\n        // Create test registration\n        const newRegistration = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).values({\n            id: (0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_4__.createId)(),\n            candidateId: candidateId,\n            candidateNumber: candidateNumber,\n            testDate: testDate,\n            testCenter: data.testCenter,\n            status: 'registered'\n        }).returning();\n        // Get the complete registration with candidate info for response\n        const registrationWithCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id,\n            candidateId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n            candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateNumber,\n            fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n            email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n            phoneNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.phoneNumber,\n            dateOfBirth: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.dateOfBirth,\n            nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n            passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n            testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testDate,\n            testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testCenter,\n            photoUrl: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoUrl,\n            photoData: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoData,\n            createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.createdAt,\n            updatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.updatedAt\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id, newRegistration[0].id)).limit(1);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...registrationWithCandidate[0],\n            isExistingCandidate\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating candidate:', error);\n        // Handle unique constraint violations\n        if (error instanceof Error && error.message.includes('unique')) {\n            if (error.message.includes('passport_number')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'A candidate with this passport number already exists'\n                }, {\n                    status: 409\n                });\n            }\n            if (error.message.includes('candidate_test_date')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Candidate is already registered for this test date'\n                }, {\n                    status: 409\n                });\n            }\n            if (error.message.includes('candidate_number_test_date')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Candidate number already exists for this test date'\n                }, {\n                    status: 409\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/candidates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testRegistrations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrations),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNsQjtBQUNHO0FBRW5DLE1BQU1HLG1CQUFtQkMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBRWpELHNFQUFzRTtBQUN0RSxNQUFNQyxTQUFTTixvREFBUUEsQ0FBQ0Usa0JBQWtCO0lBQUVLLFNBQVM7QUFBTTtBQUNwRCxNQUFNQyxLQUFLVCxnRUFBT0EsQ0FBQ08sUUFBUTtJQUFFTCxNQUFNQSxzQ0FBQUE7QUFBQyxHQUFHO0FBRXJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXGNvZGVzXFxJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbVxcc3JjXFxsaWJcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkcml6emxlIH0gZnJvbSAnZHJpenpsZS1vcm0vcG9zdGdyZXMtanMnO1xuaW1wb3J0IHBvc3RncmVzIGZyb20gJ3Bvc3RncmVzJztcbmltcG9ydCAqIGFzIHNjaGVtYSBmcm9tICcuL3NjaGVtYSc7XG5cbmNvbnN0IGNvbm5lY3Rpb25TdHJpbmcgPSBwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkwhO1xuXG4vLyBEaXNhYmxlIHByZWZldGNoIGFzIGl0IGlzIG5vdCBzdXBwb3J0ZWQgZm9yIFwiVHJhbnNhY3Rpb25cIiBwb29sIG1vZGVcbmNvbnN0IGNsaWVudCA9IHBvc3RncmVzKGNvbm5lY3Rpb25TdHJpbmcsIHsgcHJlcGFyZTogZmFsc2UgfSk7XG5leHBvcnQgY29uc3QgZGIgPSBkcml6emxlKGNsaWVudCwgeyBzY2hlbWEgfSk7XG5cbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hJztcbiJdLCJuYW1lcyI6WyJkcml6emxlIiwicG9zdGdyZXMiLCJzY2hlbWEiLCJjb25uZWN0aW9uU3RyaW5nIiwicHJvY2VzcyIsImVudiIsIkRBVEFCQVNFX1VSTCIsImNsaWVudCIsInByZXBhcmUiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testRegistrations: () => (/* binding */ testRegistrations),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table - Core candidate profile information\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test registrations table - Individual test registrations for candidates\nconst testRegistrations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_registrations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'registered',\n            'completed',\n            'cancelled'\n        ]\n    }).notNull().default('registered'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        // Composite unique constraint: same candidate cannot register for the same test date\n        uniqueCandidateTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.candidateId, table.testDate),\n        // Candidate number should be unique only within each test date\n        uniqueCandidateNumberTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.candidateNumber, table.testDate)\n    }));\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testRegistrationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_registration_id').notNull().references(()=>testRegistrations.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();