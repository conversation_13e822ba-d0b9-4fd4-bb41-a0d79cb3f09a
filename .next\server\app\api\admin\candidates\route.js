/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/candidates/route";
exports.ids = ["app/api/admin/candidates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/candidates/route.ts */ \"(rsc)/./src/app/api/admin/candidates/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/candidates/route\",\n        pathname: \"/api/admin/candidates\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/candidates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\candidates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/candidates/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/admin/candidates/route.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const search = searchParams.get('search') || '';\n        const includeResults = searchParams.get('includeResults') === 'true';\n        const offset = (page - 1) * limit;\n        // Build search conditions\n        const searchConditions = search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.candidateNumber, `%${search}%`)) : undefined;\n        if (includeResults) {\n            // Get candidates with their test results\n            const candidatesWithResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n                candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.candidateNumber,\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n                phoneNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.phoneNumber,\n                dateOfBirth: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.dateOfBirth,\n                nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n                passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n                testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testDate,\n                testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter,\n                photoUrl: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoUrl,\n                createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt,\n                updatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.updatedAt,\n                resultId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n                listeningBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.listeningBandScore,\n                readingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.readingBandScore,\n                writingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.writingBandScore,\n                speakingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.speakingBandScore,\n                overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n                status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId)).where(searchConditions).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt)).limit(limit).offset(offset);\n            // Transform the data to include hasResult flag and nested result object\n            const transformedCandidates = candidatesWithResults.map((candidate)=>({\n                    id: candidate.id,\n                    candidateNumber: candidate.candidateNumber,\n                    fullName: candidate.fullName,\n                    email: candidate.email,\n                    phoneNumber: candidate.phoneNumber,\n                    dateOfBirth: candidate.dateOfBirth,\n                    nationality: candidate.nationality,\n                    passportNumber: candidate.passportNumber,\n                    testDate: candidate.testDate,\n                    testCenter: candidate.testCenter,\n                    photoUrl: candidate.photoUrl,\n                    createdAt: candidate.createdAt,\n                    updatedAt: candidate.updatedAt,\n                    hasResult: !!candidate.resultId,\n                    result: candidate.resultId ? {\n                        id: candidate.resultId,\n                        listeningBandScore: candidate.listeningBandScore,\n                        readingBandScore: candidate.readingBandScore,\n                        writingBandScore: candidate.writingBandScore,\n                        speakingBandScore: candidate.speakingBandScore,\n                        overallBandScore: candidate.overallBandScore,\n                        status: candidate.status\n                    } : null\n                }));\n            // Get total count\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions);\n            const total = totalResult[0]?.count || 0;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                candidates: transformedCandidates,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        } else {\n            // Get total count\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions);\n            const total = totalResult[0]?.count || 0;\n            // Get candidates\n            const candidatesList = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt)).limit(limit).offset(offset);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                candidates: candidatesList,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching candidates:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'fullName',\n            'email',\n            'phoneNumber',\n            'dateOfBirth',\n            'nationality',\n            'passportNumber',\n            'testDate',\n            'testCenter'\n        ];\n        for (const field of requiredFields){\n            if (!data[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `${field} is required`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Check if this is an existing candidate registering for a new test\n        const testDate = new Date(data.testDate);\n        // Check for existing registration for the same test date\n        const existingRegistration = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, data.email), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, data.passportNumber)));\n        // Check if candidate already registered for this specific test date\n        const duplicateRegistration = existingRegistration.find((candidate)=>candidate.testDate.getTime() === testDate.getTime());\n        if (duplicateRegistration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate is already registered for this test date'\n            }, {\n                status: 409\n            });\n        }\n        // If existing candidate found, use their existing candidate number and basic info\n        let candidateNumber = data.candidateNumber;\n        let candidateData = {\n            fullName: data.fullName,\n            email: data.email,\n            phoneNumber: data.phoneNumber,\n            dateOfBirth: new Date(data.dateOfBirth),\n            nationality: data.nationality,\n            passportNumber: data.passportNumber,\n            testDate: testDate,\n            testCenter: data.testCenter,\n            photoUrl: data.photoUrl,\n            photoData: data.photoData\n        };\n        const existingCandidate = existingRegistration.find((candidate)=>candidate.email === data.email || candidate.passportNumber === data.passportNumber);\n        if (existingCandidate) {\n            // Existing candidate registering for new test - reuse their candidate number\n            candidateNumber = existingCandidate.candidateNumber;\n            // Use existing candidate's core information but allow updates for contact info and photo\n            candidateData = {\n                ...candidateData,\n                fullName: existingCandidate.fullName,\n                dateOfBirth: existingCandidate.dateOfBirth,\n                nationality: existingCandidate.nationality,\n                // Allow updates to contact info and photo for new registration\n                email: data.email,\n                phoneNumber: data.phoneNumber,\n                photoUrl: data.photoUrl || existingCandidate.photoUrl,\n                photoData: data.photoData || existingCandidate.photoData\n            };\n        } else {\n            // New candidate - generate new candidate number\n            if (!candidateNumber) {\n                // Get the count of existing candidates to generate next number\n                const candidateCount = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                    count: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id\n                }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates);\n                const nextNumber = candidateCount.length + 1;\n                candidateNumber = nextNumber.toString().padStart(3, '0');\n            }\n        }\n        // Create new test registration\n        const newCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).values({\n            candidateNumber,\n            ...candidateData\n        }).returning();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newCandidate[0], {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating candidate:', error);\n        // Handle unique constraint violations\n        if (error instanceof Error && error.message.includes('unique')) {\n            if (error.message.includes('email') && error.message.includes('test_date')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Candidate is already registered for this test date'\n                }, {\n                    status: 409\n                });\n            }\n            if (error.message.includes('passport') && error.message.includes('test_date')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Candidate with this passport number is already registered for this test date'\n                }, {\n                    status: 409\n                });\n            }\n            if (error.message.includes('candidate_number')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Candidate number already exists'\n                }, {\n                    status: 409\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/candidates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        // Composite unique constraint: same candidate cannot register for the same test date\n        uniqueCandidateTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.email, table.testDate),\n        uniquePassportTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.passportNumber, table.testDate)\n    }));\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();