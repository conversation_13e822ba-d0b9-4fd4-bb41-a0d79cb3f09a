'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeft,
  Calendar,
  MapPin,
  User,
  FileText,
  Brain,
  Award,
  AlertCircle,
  CheckCircle,
  Clock,
  ChevronDown,
  ChevronUp,
  BookOpen,
  Target,
  TrendingUp,
  Lightbulb,
  GraduationCap,
  Star,
  BarChart3,
  Headphones,
  Eye,
  PenTool,
  MessageCircle
} from 'lucide-react';
import ScoreChart from '@/components/charts/ScoreChart';
import PerformanceChart from '@/components/charts/PerformanceChart';

interface TestResult {
  id: string;
  candidateId: string;
  testDate: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  certificateSerial: string | null;
  certificateGenerated: boolean;
  aiFeedbackGenerated: boolean;
  createdAt: string;
  updatedAt: string;
  candidate: {
    fullName: string;
    nationality: string;
    testDate: string;
    testCenter: string;
    photoUrl: string | null;
  };
  performanceMetrics: {
    averageScore: number | null;
    highestScore: number | null;
    lowestScore: number | null;
    scoreDistribution: {
      listening?: number | null;
      reading?: number | null;
      writing?: number | null;
      speaking?: number | null;
    };
  };
}

interface AIFeedback {
  id: string;
  testResultId: string;
  listeningFeedback: string | null;
  readingFeedback: string | null;
  writingFeedback: string | null;
  speakingFeedback: string | null;
  overallFeedback: string | null;
  studyRecommendations: string | null;
  generatedAt: string;
}

export default function PublicResultsPage() {
  const params = useParams();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [feedback, setFeedback] = useState<AIFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('scores');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    listening: false,
    reading: false,
    writing: false,
    speaking: false,
    bandExplanations: false,
    studyTips: false,
    nextSteps: false
  });

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  const fetchFeedback = useCallback(async () => {
    if (!result?.aiFeedbackGenerated) return;

    setFeedbackLoading(true);
    try {
      const response = await fetch(`/api/feedback/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setFeedback(data);
      }
    } catch (error) {
      console.error('Error fetching feedback:', error);
    } finally {
      setFeedbackLoading(false);
    }
  }, [resultId, result?.aiFeedbackGenerated]);

  useEffect(() => {
    fetchResult();
  }, [fetchResult]);

  useEffect(() => {
    if (result) {
      fetchFeedback();
    }
  }, [fetchFeedback, result]);



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'verified':
        return <Award className="h-5 w-5 text-blue-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-800 bg-yellow-100';
      case 'completed':
        return 'text-green-800 bg-green-100';
      case 'verified':
        return 'text-blue-800 bg-blue-100';
      default:
        return 'text-gray-800 bg-gray-100';
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getBandScoreDescription = (score: number | null) => {
    if (!score) return null;

    if (score >= 9) return { level: 'Expert User', description: 'Has fully operational command of the language with only occasional unsystematic inaccuracies.' };
    if (score >= 8) return { level: 'Very Good User', description: 'Has fully operational command of the language with only occasional unsystematic inaccuracies and inappropriate usage in some unfamiliar situations.' };
    if (score >= 7) return { level: 'Good User', description: 'Has operational command of the language, though with occasional inaccuracies, inappropriate usage and misunderstandings in some situations.' };
    if (score >= 6) return { level: 'Competent User', description: 'Has generally effective command of the language despite some inaccuracies, inappropriate usage and misunderstandings.' };
    if (score >= 5) return { level: 'Modest User', description: 'Has partial command of the language, coping with overall meaning in most situations, though is likely to make many mistakes.' };
    if (score >= 4) return { level: 'Limited User', description: 'Basic competence is limited to familiar situations. Has frequent problems in understanding and expression.' };
    return { level: 'Extremely Limited User', description: 'Conveys and understands only general meaning in very familiar situations. Frequent breakdowns in communication occur.' };
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your results...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Unable to Load Results</h3>
          <p className="text-gray-600 mb-6">{error || 'Result not found'}</p>
          <Link
            href="/search"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/search" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Back to Search
              </Link>
              <FileText className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">IELTS Test Results</h1>
                <p className="text-gray-600">Official Test Report</p>
              </div>
            </div>

          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Candidate Info & Status */}
          <div className="lg:col-span-1 space-y-6">
            {/* Candidate Information */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Candidate Information</h2>
              <div className="flex items-start space-x-4">
                {result.candidate.photoUrl ? (
                  <Image
                    src={result.candidate.photoUrl}
                    alt={result.candidate.fullName}
                    width={80}
                    height={80}
                    className="rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                    <User className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900">{result.candidate.fullName}</h4>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      {result.candidate.nationality}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      Test Center: {result.candidate.testCenter}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Result Status */}
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Result Status</h2>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {getStatusIcon(result.status)}
                  <span className={`ml-3 px-3 py-1 rounded-full text-sm font-medium ${getStatusBadge(result.status)}`}>
                    {result.status.charAt(0).toUpperCase() + result.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="mt-4 text-sm text-gray-600">
                <p>Result ID: {result.id}</p>
                {result.certificateSerial && (
                  <p>Certificate Serial: {result.certificateSerial}</p>
                )}
                <p>Generated: {new Date(result.createdAt).toLocaleDateString()}</p>
              </div>
            </div>

            {/* Overall Score */}
            {result.overallBandScore && (
              <div className="bg-gradient-to-br from-indigo-500 to-purple-600 shadow rounded-lg p-6 text-white text-center">
                <h2 className="text-lg font-medium mb-4">Overall Band Score</h2>
                <div className="text-5xl font-bold mb-2">
                  {result.overallBandScore}
                </div>
                <p className="text-indigo-100">IELTS Band Score</p>
                <div className="mt-4 text-sm">
                  {result.overallBandScore >= 8.5 && <p>Expert User</p>}
                  {result.overallBandScore >= 7.5 && result.overallBandScore < 8.5 && <p>Very Good User</p>}
                  {result.overallBandScore >= 6.5 && result.overallBandScore < 7.5 && <p>Good User</p>}
                  {result.overallBandScore >= 5.5 && result.overallBandScore < 6.5 && <p>Modest User</p>}
                  {result.overallBandScore < 5.5 && <p>Limited User</p>}
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Scores & Performance */}
          <div className="lg:col-span-2 space-y-6">
            {/* Score Charts */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <ScoreChart
                scores={{
                  listening: result.listeningBandScore,
                  reading: result.readingBandScore,
                  writing: result.writingBandScore,
                  speaking: result.speakingBandScore,
                  overall: result.overallBandScore,
                }}
              />
              <PerformanceChart
                metrics={result.performanceMetrics}
                overallScore={result.overallBandScore}
              />
            </div>

            {/* Enhanced Results Section with Tabs */}
            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-medium text-gray-900">Detailed Results & Analysis</h2>
              </div>

              {/* Tab Navigation */}
              <div className="border-b border-gray-200 mb-6">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setActiveTab('scores')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'scores'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <BarChart3 className="h-4 w-4 inline mr-2" />
                    Score Breakdown
                  </button>
                  <button
                    onClick={() => setActiveTab('analysis')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'analysis'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Target className="h-4 w-4 inline mr-2" />
                    Performance Analysis
                  </button>
                  <button
                    onClick={() => setActiveTab('guidance')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'guidance'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <GraduationCap className="h-4 w-4 inline mr-2" />
                    Study Guidance
                  </button>
                </nav>
              </div>

              {/* Tab Content */}
              {activeTab === 'scores' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Listening */}
                    {result.listeningBandScore && (
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-md font-semibold text-blue-600 flex items-center">
                            <Headphones className="h-4 w-4 mr-2" />
                            Listening
                          </h3>
                          <button
                            onClick={() => toggleSection('listening')}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            {expandedSections.listening ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </button>
                        </div>
                        <div className="space-y-2">
                          {result.listeningScore && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Raw Score:</span>
                              <span className="font-medium">{result.listeningScore}/40</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Band Score:</span>
                            <span className="text-lg font-bold text-blue-600">{result.listeningBandScore}</span>
                          </div>
                          {getBandScoreDescription(result.listeningBandScore) && (
                            <div className="mt-2 p-2 bg-blue-50 rounded">
                              <p className="text-xs font-medium text-blue-800">
                                {getBandScoreDescription(result.listeningBandScore)?.level}
                              </p>
                              <p className="text-xs text-blue-700 mt-1">
                                {getBandScoreDescription(result.listeningBandScore)?.description}
                              </p>
                            </div>
                          )}
                        </div>

                        {expandedSections.listening && (
                          <div className="mt-4 pt-4 border-t border-gray-200">
                            <h4 className="font-medium text-gray-900 mb-2">Listening Skills Assessment</h4>
                            <div className="space-y-2 text-sm text-gray-700">
                              <p>• <strong>Understanding main ideas:</strong> {result.listeningBandScore >= 7 ? 'Excellent' : result.listeningBandScore >= 6 ? 'Good' : result.listeningBandScore >= 5 ? 'Adequate' : 'Needs improvement'}</p>
                              <p>• <strong>Following detailed arguments:</strong> {result.listeningBandScore >= 7 ? 'Strong ability' : result.listeningBandScore >= 6 ? 'Generally effective' : 'Room for improvement'}</p>
                              <p>• <strong>Understanding attitudes and opinions:</strong> {result.listeningBandScore >= 8 ? 'Highly developed' : result.listeningBandScore >= 6 ? 'Developing well' : 'Needs focus'}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Reading */}
                    {result.readingBandScore && (
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-md font-semibold text-green-600 flex items-center">
                            <Eye className="h-4 w-4 mr-2" />
                            Reading
                          </h3>
                          <button
                            onClick={() => toggleSection('reading')}
                            className="text-green-600 hover:text-green-700"
                          >
                            {expandedSections.reading ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </button>
                        </div>
                        <div className="space-y-2">
                          {result.readingScore && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Raw Score:</span>
                              <span className="font-medium">{result.readingScore}/40</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Band Score:</span>
                            <span className="text-lg font-bold text-green-600">{result.readingBandScore}</span>
                          </div>
                          {getBandScoreDescription(result.readingBandScore) && (
                            <div className="mt-2 p-2 bg-green-50 rounded">
                              <p className="text-xs font-medium text-green-800">
                                {getBandScoreDescription(result.readingBandScore)?.level}
                              </p>
                              <p className="text-xs text-green-700 mt-1">
                                {getBandScoreDescription(result.readingBandScore)?.description}
                              </p>
                            </div>
                          )}
                        </div>

                        {expandedSections.reading && (
                          <div className="mt-4 pt-4 border-t border-gray-200">
                            <h4 className="font-medium text-gray-900 mb-2">Reading Skills Assessment</h4>
                            <div className="space-y-2 text-sm text-gray-700">
                              <p>• <strong>Reading for gist:</strong> {result.readingBandScore >= 7 ? 'Excellent' : result.readingBandScore >= 6 ? 'Good' : result.readingBandScore >= 5 ? 'Adequate' : 'Needs improvement'}</p>
                              <p>• <strong>Reading for detail:</strong> {result.readingBandScore >= 7 ? 'Strong ability' : result.readingBandScore >= 6 ? 'Generally effective' : 'Room for improvement'}</p>
                              <p>• <strong>Understanding inferences:</strong> {result.readingBandScore >= 8 ? 'Highly developed' : result.readingBandScore >= 6 ? 'Developing well' : 'Needs focus'}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Writing */}
                    {result.writingBandScore && (
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-md font-semibold text-yellow-600 flex items-center">
                            <PenTool className="h-4 w-4 mr-2" />
                            Writing
                          </h3>
                          <button
                            onClick={() => toggleSection('writing')}
                            className="text-yellow-600 hover:text-yellow-700"
                          >
                            {expandedSections.writing ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </button>
                        </div>
                        <div className="space-y-2">
                          {result.writingTask1Score && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Task 1:</span>
                              <span className="font-medium">{result.writingTask1Score}/9</span>
                            </div>
                          )}
                          {result.writingTask2Score && (
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Task 2:</span>
                              <span className="font-medium">{result.writingTask2Score}/9</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Band Score:</span>
                            <span className="text-lg font-bold text-yellow-600">{result.writingBandScore}</span>
                          </div>
                          {getBandScoreDescription(result.writingBandScore) && (
                            <div className="mt-2 p-2 bg-yellow-50 rounded">
                              <p className="text-xs font-medium text-yellow-800">
                                {getBandScoreDescription(result.writingBandScore)?.level}
                              </p>
                              <p className="text-xs text-yellow-700 mt-1">
                                {getBandScoreDescription(result.writingBandScore)?.description}
                              </p>
                            </div>
                          )}
                        </div>

                        {expandedSections.writing && (
                          <div className="mt-4 pt-4 border-t border-gray-200">
                            <h4 className="font-medium text-gray-900 mb-2">Writing Skills Assessment</h4>
                            <div className="space-y-2 text-sm text-gray-700">
                              <p>• <strong>Task Achievement:</strong> {result.writingBandScore >= 7 ? 'Excellent' : result.writingBandScore >= 6 ? 'Good' : result.writingBandScore >= 5 ? 'Adequate' : 'Needs improvement'}</p>
                              <p>• <strong>Coherence & Cohesion:</strong> {result.writingBandScore >= 7 ? 'Strong organization' : result.writingBandScore >= 6 ? 'Generally well organized' : 'Room for improvement'}</p>
                              <p>• <strong>Lexical Resource:</strong> {result.writingBandScore >= 8 ? 'Wide range of vocabulary' : result.writingBandScore >= 6 ? 'Adequate vocabulary' : 'Limited vocabulary range'}</p>
                              <p>• <strong>Grammar & Accuracy:</strong> {result.writingBandScore >= 8 ? 'Highly accurate' : result.writingBandScore >= 6 ? 'Generally accurate' : 'Frequent errors'}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Speaking */}
                    {result.speakingBandScore && (
                      <div className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="text-md font-semibold text-purple-600 flex items-center">
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Speaking
                          </h3>
                          <button
                            onClick={() => toggleSection('speaking')}
                            className="text-purple-600 hover:text-purple-700"
                          >
                            {expandedSections.speaking ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </button>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Band Score:</span>
                            <span className="text-lg font-bold text-purple-600">{result.speakingBandScore}</span>
                          </div>
                          {getBandScoreDescription(result.speakingBandScore) && (
                            <div className="mt-2 p-2 bg-purple-50 rounded">
                              <p className="text-xs font-medium text-purple-800">
                                {getBandScoreDescription(result.speakingBandScore)?.level}
                              </p>
                              <p className="text-xs text-purple-700 mt-1">
                                {getBandScoreDescription(result.speakingBandScore)?.description}
                              </p>
                            </div>
                          )}
                        </div>

                        {expandedSections.speaking && (
                          <div className="mt-4 pt-4 border-t border-gray-200">
                            <h4 className="font-medium text-gray-900 mb-2">Speaking Skills Assessment</h4>
                            <div className="space-y-2 text-sm text-gray-700">
                              <p>• <strong>Fluency & Coherence:</strong> {result.speakingBandScore >= 7 ? 'Speaks fluently' : result.speakingBandScore >= 6 ? 'Generally fluent' : result.speakingBandScore >= 5 ? 'Some hesitation' : 'Frequent hesitation'}</p>
                              <p>• <strong>Lexical Resource:</strong> {result.speakingBandScore >= 7 ? 'Wide vocabulary range' : result.speakingBandScore >= 6 ? 'Adequate vocabulary' : 'Limited vocabulary'}</p>
                              <p>• <strong>Grammar & Accuracy:</strong> {result.speakingBandScore >= 8 ? 'Highly accurate' : result.speakingBandScore >= 6 ? 'Generally accurate' : 'Frequent errors'}</p>
                              <p>• <strong>Pronunciation:</strong> {result.speakingBandScore >= 8 ? 'Excellent pronunciation' : result.speakingBandScore >= 6 ? 'Generally clear' : 'Needs improvement'}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Performance Analysis Tab */}
              {activeTab === 'analysis' && (
                <div className="space-y-6">
                  {/* Overall Performance Summary */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                      Performance Summary
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{result.overallBandScore}</div>
                        <div className="text-sm text-gray-600">Overall Band Score</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {Math.max(
                            result.listeningBandScore || 0,
                            result.readingBandScore || 0,
                            result.writingBandScore || 0,
                            result.speakingBandScore || 0
                          )}
                        </div>
                        <div className="text-sm text-gray-600">Highest Score</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {Math.min(
                            ...[result.listeningBandScore, result.readingBandScore, result.writingBandScore, result.speakingBandScore]
                              .filter(score => score !== null) as number[]
                          )}
                        </div>
                        <div className="text-sm text-gray-600">Lowest Score</div>
                      </div>
                    </div>
                  </div>

                  {/* Strengths and Areas for Improvement */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-green-50 rounded-lg p-6">
                      <h4 className="font-semibold text-green-800 mb-3 flex items-center">
                        <Star className="h-4 w-4 mr-2" />
                        Strengths
                      </h4>
                      <div className="space-y-2 text-sm text-green-700">
                        {result.listeningBandScore && result.listeningBandScore >= 7 && (
                          <p>• Strong listening comprehension skills</p>
                        )}
                        {result.readingBandScore && result.readingBandScore >= 7 && (
                          <p>• Excellent reading comprehension abilities</p>
                        )}
                        {result.writingBandScore && result.writingBandScore >= 7 && (
                          <p>• Effective written communication skills</p>
                        )}
                        {result.speakingBandScore && result.speakingBandScore >= 7 && (
                          <p>• Confident oral communication abilities</p>
                        )}
                        {result.overallBandScore && result.overallBandScore >= 7 && (
                          <p>• Overall strong English proficiency</p>
                        )}
                        {![result.listeningBandScore, result.readingBandScore, result.writingBandScore, result.speakingBandScore]
                          .some(score => score && score >= 7) && (
                          <p>• Demonstrates foundational English skills</p>
                        )}
                      </div>
                    </div>

                    <div className="bg-orange-50 rounded-lg p-6">
                      <h4 className="font-semibold text-orange-800 mb-3 flex items-center">
                        <Target className="h-4 w-4 mr-2" />
                        Areas for Improvement
                      </h4>
                      <div className="space-y-2 text-sm text-orange-700">
                        {/* Specific module improvements */}
                        {result.listeningBandScore && result.listeningBandScore < 6.5 && (
                          <p>• Focus on listening comprehension practice</p>
                        )}
                        {result.readingBandScore && result.readingBandScore < 6.5 && (
                          <p>• Enhance reading speed and comprehension</p>
                        )}
                        {result.writingBandScore && result.writingBandScore < 6.5 && (
                          <p>• Develop writing structure and vocabulary</p>
                        )}
                        {result.speakingBandScore && result.speakingBandScore < 6.5 && (
                          <p>• Practice speaking fluency and pronunciation</p>
                        )}
                        {result.overallBandScore && result.overallBandScore < 6.5 && (
                          <p>• Overall English proficiency development needed</p>
                        )}

                        {/* General improvements for mid-level performers */}
                        {result.overallBandScore && result.overallBandScore >= 6.5 && result.overallBandScore < 7.5 && (
                          <>
                            <p>• Expand academic vocabulary range</p>
                            <p>• Practice complex grammatical structures</p>
                            <p>• Improve consistency across all modules</p>
                            <p>• Work on time management strategies</p>
                          </>
                        )}

                        {/* Improvements for high performers */}
                        {result.overallBandScore && result.overallBandScore >= 7.5 && (
                          <>
                            <p>• Fine-tune pronunciation and intonation</p>
                            <p>• Master advanced vocabulary and idioms</p>
                            <p>• Perfect essay coherence and cohesion</p>
                            <p>• Develop sophisticated argumentation skills</p>
                          </>
                        )}

                        {/* Fallback content if no overall score */}
                        {!result.overallBandScore && (
                          <>
                            <p>• Regular practice across all four skills</p>
                            <p>• Build foundational vocabulary</p>
                            <p>• Improve basic grammar accuracy</p>
                            <p>• Develop test-taking strategies</p>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Score Comparison */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 mb-4">Score Comparison with Global Averages</h4>
                    <div className="space-y-4">
                      {result.listeningBandScore && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Listening</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Global Avg: 6.0</span>
                            <span className={`text-sm font-medium ${result.listeningBandScore >= 6.0 ? 'text-green-600' : 'text-orange-600'}`}>
                              Your Score: {result.listeningBandScore}
                            </span>
                            {result.listeningBandScore >= 6.0 ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <Target className="h-4 w-4 text-orange-600" />
                            )}
                          </div>
                        </div>
                      )}
                      {result.readingBandScore && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Reading</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Global Avg: 6.0</span>
                            <span className={`text-sm font-medium ${result.readingBandScore >= 6.0 ? 'text-green-600' : 'text-orange-600'}`}>
                              Your Score: {result.readingBandScore}
                            </span>
                            {result.readingBandScore >= 6.0 ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <Target className="h-4 w-4 text-orange-600" />
                            )}
                          </div>
                        </div>
                      )}
                      {result.writingBandScore && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Writing</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Global Avg: 5.5</span>
                            <span className={`text-sm font-medium ${result.writingBandScore >= 5.5 ? 'text-green-600' : 'text-orange-600'}`}>
                              Your Score: {result.writingBandScore}
                            </span>
                            {result.writingBandScore >= 5.5 ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <Target className="h-4 w-4 text-orange-600" />
                            )}
                          </div>
                        </div>
                      )}
                      {result.speakingBandScore && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">Speaking</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Global Avg: 6.0</span>
                            <span className={`text-sm font-medium ${result.speakingBandScore >= 6.0 ? 'text-green-600' : 'text-orange-600'}`}>
                              Your Score: {result.speakingBandScore}
                            </span>
                            {result.speakingBandScore >= 6.0 ? (
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                              <Target className="h-4 w-4 text-orange-600" />
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Study Guidance Tab */}
              {activeTab === 'guidance' && (
                <div className="space-y-6">
                  {/* Personalized Study Plan */}
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <BookOpen className="h-5 w-5 mr-2 text-purple-600" />
                      Personalized Study Plan
                    </h3>
                    <p className="text-gray-700 mb-4">
                      Based on your current performance, here is a tailored study plan to help you improve your IELTS scores:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Priority Areas</h4>
                        <div className="space-y-1 text-sm text-gray-700">
                          {result.writingBandScore && result.writingBandScore < 6.5 && (
                            <p>• Writing skills development (High Priority)</p>
                          )}
                          {result.speakingBandScore && result.speakingBandScore < 6.5 && (
                            <p>• Speaking fluency practice (High Priority)</p>
                          )}
                          {result.listeningBandScore && result.listeningBandScore < 6.5 && (
                            <p>• Listening comprehension (Medium Priority)</p>
                          )}
                          {result.readingBandScore && result.readingBandScore < 6.5 && (
                            <p>• Reading speed and accuracy (Medium Priority)</p>
                          )}
                          {/* Add content for high performers */}
                          {result.overallBandScore && result.overallBandScore >= 7 && (
                            <>
                              <p>• Maintain current proficiency level</p>
                              <p>• Focus on consistency across all modules</p>
                              <p>• Consider advanced English certifications</p>
                            </>
                          )}
                          {/* Add content when no specific weaknesses */}
                          {!([result.writingBandScore, result.speakingBandScore, result.listeningBandScore, result.readingBandScore]
                            .some(score => score && score < 6.5)) && (
                            <>
                              <p>• Continue regular practice to maintain skills</p>
                              <p>• Focus on advanced vocabulary development</p>
                              <p>• Practice complex grammatical structures</p>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="bg-white rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-2">Study Schedule</h4>
                        <div className="space-y-1 text-sm text-gray-700">
                          <p>• Daily practice: {result.overallBandScore && result.overallBandScore >= 7 ? '1-2 hours' : '2-3 hours'}</p>
                          <p>• Focus sessions: 45-60 minutes</p>
                          <p>• Weekly mock tests</p>
                          <p>• Regular progress reviews</p>
                          <p>• Vocabulary building: 15 minutes daily</p>
                          <p>• Grammar exercises: 20 minutes daily</p>
                          {result.overallBandScore && result.overallBandScore < 6 && (
                            <>
                              <p>• Foundation skills: 30 minutes daily</p>
                              <p>• Basic conversation practice</p>
                            </>
                          )}
                          {result.overallBandScore && result.overallBandScore >= 7 && (
                            <>
                              <p>• Advanced practice materials</p>
                              <p>• Academic English focus</p>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Module-specific Tips */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Listening Tips */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h4 className="font-semibold text-blue-600 mb-3 flex items-center">
                        <Headphones className="h-4 w-4 mr-2" />
                        Listening Improvement Tips
                      </h4>
                      <div className="space-y-2 text-sm text-gray-700">
                        <p>• Practice with BBC podcasts and TED talks</p>
                        <p>• Focus on different English accents (British, American, Australian)</p>
                        <p>• Take notes while listening using abbreviations</p>
                        <p>• Practice predicting content from context</p>
                        <p>• Work on identifying key information quickly</p>
                        <p>• Listen to academic lectures and news broadcasts</p>
                        <p>• Practice with IELTS listening test formats</p>
                        <p>• Improve concentration and focus techniques</p>
                        <p>• Use subtitles gradually (start with, then remove)</p>
                        <p>• Practice listening for specific information vs. general understanding</p>
                      </div>
                    </div>

                    {/* Reading Tips */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h4 className="font-semibold text-green-600 mb-3 flex items-center">
                        <Eye className="h-4 w-4 mr-2" />
                        Reading Improvement Tips
                      </h4>
                      <div className="space-y-2 text-sm text-gray-700">
                        <p>• Practice skimming and scanning techniques</p>
                        <p>• Read academic articles daily (The Economist, Scientific American)</p>
                        <p>• Build vocabulary systematically with context</p>
                        <p>• Time yourself during practice (20 minutes per passage)</p>
                        <p>• Focus on understanding main ideas first</p>
                        <p>• Practice different question types (multiple choice, matching, T/F/NG)</p>
                        <p>• Improve reading speed without losing comprehension</p>
                        <p>• Learn to identify paragraph themes quickly</p>
                        <p>• Practice paraphrasing and synonym recognition</p>
                        <p>• Read diverse topics (science, history, social issues)</p>
                      </div>
                    </div>

                    {/* Writing Tips */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h4 className="font-semibold text-yellow-600 mb-3 flex items-center">
                        <PenTool className="h-4 w-4 mr-2" />
                        Writing Improvement Tips
                      </h4>
                      <div className="space-y-2 text-sm text-gray-700">
                        <p>• Practice essay structure (intro-body-conclusion)</p>
                        <p>• Learn linking words and phrases (however, furthermore, consequently)</p>
                        <p>• Expand academic vocabulary and collocations</p>
                        <p>• Practice both Task 1 and Task 2 regularly</p>
                        <p>• Get feedback on your writing from teachers or online platforms</p>
                        <p>• Master different Task 1 formats (graphs, charts, diagrams, maps)</p>
                        <p>• Practice Task 2 essay types (opinion, discussion, problem-solution)</p>
                        <p>• Improve grammar accuracy and sentence variety</p>
                        <p>• Learn to paraphrase effectively</p>
                        <p>• Practice time management (20 min Task 1, 40 min Task 2)</p>
                      </div>
                    </div>

                    {/* Speaking Tips */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h4 className="font-semibold text-purple-600 mb-3 flex items-center">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Speaking Improvement Tips
                      </h4>
                      <div className="space-y-2 text-sm text-gray-700">
                        <p>• Practice speaking daily for 15-20 minutes</p>
                        <p>• Record yourself and analyze fluency and pronunciation</p>
                        <p>• Work on pronunciation with native speakers or apps</p>
                        <p>• Practice all three parts of the speaking test</p>
                        <p>• Build confidence through regular practice</p>
                        <p>• Master Part 1 (personal questions) with natural responses</p>
                        <p>• Practice Part 2 (long turn) with 2-minute speeches</p>
                        <p>• Develop Part 3 (discussion) with complex ideas and opinions</p>
                        <p>• Improve intonation and stress patterns</p>
                        <p>• Practice thinking in English to reduce hesitation</p>
                      </div>
                    </div>
                  </div>

                  {/* Recommended Resources */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                      <Lightbulb className="h-4 w-4 mr-2 text-yellow-500" />
                      Recommended Study Resources
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <h5 className="font-medium text-gray-800 mb-2">Official Materials</h5>
                        <div className="space-y-1 text-sm text-gray-600">
                          <p>• Cambridge IELTS Practice Tests (Books 1-18)</p>
                          <p>• Official IELTS Preparation Materials</p>
                          <p>• IELTS.org Practice Tests</p>
                          <p>• Cambridge English Vocabulary for IELTS</p>
                          <p>• Official IELTS Practice Materials 2</p>
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-800 mb-2">Online Platforms</h5>
                        <div className="space-y-1 text-sm text-gray-600">
                          <p>• IELTS Liz (Free lessons and tips)</p>
                          <p>• Magoosh IELTS Prep</p>
                          <p>• British Council IELTS Prep</p>
                          <p>• IDP IELTS Preparation</p>
                          <p>• IELTS Simon (Writing & Speaking)</p>
                          <p>• Road to IELTS (British Council)</p>
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-800 mb-2">Apps & Tools</h5>
                        <div className="space-y-1 text-sm text-gray-600">
                          <p>• IELTS Prep App (Official)</p>
                          <p>• Grammarly (Writing assistance)</p>
                          <p>• ELSA Speak (Pronunciation)</p>
                          <p>• Anki (Vocabulary flashcards)</p>
                          <p>• BBC Learning English</p>
                          <p>• Cambridge Dictionary</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Next Steps */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
                    <h4 className="font-semibold text-green-800 mb-3 flex items-center">
                      <Target className="h-4 w-4 mr-2" />
                      Your Next Steps
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                        <p className="text-sm text-green-700">
                          <strong>Immediate (This Week):</strong> Focus on your weakest skill area and start daily practice routine
                        </p>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
                        <p className="text-sm text-green-700">
                          <strong>Short-term (2-4 Weeks):</strong> Take practice tests to track improvement and adjust study plan
                        </p>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
                        <p className="text-sm text-green-700">
                          <strong>Long-term (1-3 Months):</strong> Consider retaking IELTS when you consistently score your target band in practice tests
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* AI Feedback Section */}
            {result.aiFeedbackGenerated && (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="flex items-center mb-6">
                  <Brain className="h-6 w-6 text-purple-600 mr-2" />
                  <h2 className="text-lg font-medium text-gray-900">AI-Generated Feedback</h2>
                </div>

                {feedbackLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading feedback...</p>
                  </div>
                ) : feedback ? (
                  <div className="space-y-6">
                    {/* Module-specific feedback */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {feedback.listeningFeedback && (
                        <div className="border-l-4 border-blue-500 pl-4">
                          <h4 className="font-semibold text-blue-600 mb-2">Listening Feedback</h4>
                          <p className="text-sm text-gray-700">{feedback.listeningFeedback}</p>
                        </div>
                      )}
                      {feedback.readingFeedback && (
                        <div className="border-l-4 border-green-500 pl-4">
                          <h4 className="font-semibold text-green-600 mb-2">Reading Feedback</h4>
                          <p className="text-sm text-gray-700">{feedback.readingFeedback}</p>
                        </div>
                      )}
                      {feedback.writingFeedback && (
                        <div className="border-l-4 border-yellow-500 pl-4">
                          <h4 className="font-semibold text-yellow-600 mb-2">Writing Feedback</h4>
                          <p className="text-sm text-gray-700">{feedback.writingFeedback}</p>
                        </div>
                      )}
                      {feedback.speakingFeedback && (
                        <div className="border-l-4 border-purple-500 pl-4">
                          <h4 className="font-semibold text-purple-600 mb-2">Speaking Feedback</h4>
                          <p className="text-sm text-gray-700">{feedback.speakingFeedback}</p>
                        </div>
                      )}
                    </div>

                    {/* Overall feedback */}
                    {feedback.overallFeedback && (
                      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4">
                        <h4 className="font-semibold text-purple-600 mb-2">Overall Assessment</h4>
                        <p className="text-gray-700">{feedback.overallFeedback}</p>
                      </div>
                    )}

                    {/* Study recommendations */}
                    {feedback.studyRecommendations && (
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4">
                        <h4 className="font-semibold text-green-600 mb-2">Study Recommendations</h4>
                        <p className="text-gray-700">{feedback.studyRecommendations}</p>
                      </div>
                    )}

                    <div className="text-xs text-gray-500 text-center">
                      Feedback generated on {new Date(feedback.generatedAt).toLocaleDateString()}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">AI feedback is not available at this time.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 bg-white shadow rounded-lg p-6 text-center">
          <div className="flex items-center justify-center mb-4">
            <Award className="h-8 w-8 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Official IELTS Test Report</h3>
          </div>
          <p className="text-gray-600 mb-4">
            This is an official IELTS test result. For verification purposes, please use the result ID: {result.id}
          </p>
          {result.certificateSerial && (
            <p className="text-sm text-gray-500">
              Certificate Serial Number: {result.certificateSerial}
            </p>
          )}
          <div className="mt-4 flex justify-center space-x-4">
            <Link
              href="/search"
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Search Other Results
            </Link>
            {result.certificateSerial && (
              <Link
                href={`/verify/${result.certificateSerial}`}
                className="text-green-600 hover:text-green-700 text-sm font-medium"
              >
                Verify Certificate
              </Link>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}