import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: candidateId } = await params;

    // Get candidate details
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!candidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Get all test results for this candidate
    const candidateTestResults = await db
      .select()
      .from(testResults)
      .where(eq(testResults.candidateId, candidateId))
      .orderBy(desc(testResults.createdAt));

    // Combine candidate data with test results
    const candidateWithResults = {
      ...candidate[0],
      testResults: candidateTestResults
    };

    return NextResponse.json(candidateWithResults);
  } catch (error) {
    console.error('Error fetching candidate details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: candidateId } = await params;
    const data = await request.json();

    // Validate required fields
    const requiredFields = [
      'fullName',
      'email',
      'phoneNumber',
      'dateOfBirth',
      'nationality',
      'passportNumber',
      'testDate',
      'testCenter'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Check if candidate exists
    const existingCandidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!existingCandidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Update candidate
    const updatedCandidate = await db
      .update(candidates)
      .set({
        fullName: data.fullName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        dateOfBirth: data.dateOfBirth,
        nationality: data.nationality,
        passportNumber: data.passportNumber,
        testDate: data.testDate,
        testCenter: data.testCenter,
        photoData: data.photoData || null,
        photoUrl: data.photoUrl || null,
        updatedAt: new Date(),
      })
      .where(eq(candidates.id, candidateId))
      .returning();

    return NextResponse.json(updatedCandidate[0]);
  } catch (error) {
    console.error('Error updating candidate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: candidateId } = await params;

    // Check if candidate exists
    const existingCandidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!existingCandidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Check if candidate has test results
    const candidateResults = await db
      .select()
      .from(testResults)
      .where(eq(testResults.candidateId, candidateId))
      .limit(1);

    if (candidateResults.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete candidate with existing test results' },
        { status: 400 }
      );
    }

    // Delete candidate
    await db
      .delete(candidates)
      .where(eq(candidates.id, candidateId));

    return NextResponse.json({ message: 'Candidate deleted successfully' });
  } catch (error) {
    console.error('Error deleting candidate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
