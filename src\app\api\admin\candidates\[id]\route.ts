import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // First, try to find if this ID is a candidate ID or a test registration ID
    let candidateId: string;

    // Check if it's a candidate ID
    const candidateCheck = await db
      .select({ id: candidates.id })
      .from(candidates)
      .where(eq(candidates.id, id))
      .limit(1);

    if (candidateCheck.length > 0) {
      candidateId = id;
    } else {
      // Check if it's a test registration ID
      const registrationCheck = await db
        .select({ candidateId: testRegistrations.candidateId })
        .from(testRegistrations)
        .where(eq(testRegistrations.id, id))
        .limit(1);

      if (registrationCheck.length > 0) {
        candidateId = registrationCheck[0].candidateId;
      } else {
        return NextResponse.json(
          { error: 'Candidate not found' },
          { status: 404 }
        );
      }
    }

    // Get candidate details
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    // Get all test registrations for this candidate
    const candidateRegistrations = await db
      .select()
      .from(testRegistrations)
      .where(eq(testRegistrations.candidateId, candidateId))
      .orderBy(desc(testRegistrations.testDate));

    // Get all test results for this candidate's registrations
    const registrationIds = candidateRegistrations.map(reg => reg.id);
    let candidateTestResults: any[] = [];

    if (registrationIds.length > 0) {
      candidateTestResults = await db
        .select({
          ...testResults,
          registrationInfo: {
            candidateNumber: testRegistrations.candidateNumber,
            testDate: testRegistrations.testDate,
            testCenter: testRegistrations.testCenter,
          }
        })
        .from(testResults)
        .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
        .where(eq(testRegistrations.candidateId, candidateId))
        .orderBy(desc(testResults.createdAt));
    }

    // Combine candidate data with registrations and test results
    const candidateWithResults = {
      ...candidate[0],
      testRegistrations: candidateRegistrations,
      testResults: candidateTestResults
    };

    return NextResponse.json(candidateWithResults);
  } catch (error) {
    console.error('Error fetching candidate details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: candidateId } = await params;
    const data = await request.json();

    // Validate required fields for candidate profile
    const requiredFields = [
      'fullName',
      'email',
      'phoneNumber',
      'dateOfBirth',
      'nationality',
      'passportNumber'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Check if candidate exists
    const existingCandidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!existingCandidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Update candidate profile only
    const updatedCandidate = await db
      .update(candidates)
      .set({
        fullName: data.fullName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        dateOfBirth: new Date(data.dateOfBirth),
        nationality: data.nationality,
        passportNumber: data.passportNumber,
        photoData: data.photoData || null,
        photoUrl: data.photoUrl || null,
        updatedAt: new Date(),
      })
      .where(eq(candidates.id, candidateId))
      .returning();

    return NextResponse.json(updatedCandidate[0]);
  } catch (error) {
    console.error('Error updating candidate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: candidateId } = await params;

    // Check if candidate exists
    const existingCandidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!existingCandidate.length) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Check if candidate has test registrations
    const candidateRegistrations = await db
      .select()
      .from(testRegistrations)
      .where(eq(testRegistrations.candidateId, candidateId))
      .limit(1);

    if (candidateRegistrations.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete candidate with existing test registrations' },
        { status: 400 }
      );
    }

    // Delete candidate
    await db
      .delete(candidates)
      .where(eq(candidates.id, candidateId));

    return NextResponse.json({ message: 'Candidate deleted successfully' });
  } catch (error) {
    console.error('Error deleting candidate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
