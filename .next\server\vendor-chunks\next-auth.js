"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(ssr)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ \n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...req?.headers?.cookie ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(ssr)/./node_modules/next-auth/lib/client.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ \n\n\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                })}`;\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href } = options ?? {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async ({ event } = {})=>{\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n\nconst __NEXTAUTH = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call __NEXTAUTH() from the server but __NEXTAUTH is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"__NEXTAUTH\",\n);const SessionContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SessionContext() from the server but SessionContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"SessionContext\",\n);const useSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"useSession\",\n);const getSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getSession() from the server but getSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getSession\",\n);const getCsrfToken = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getCsrfToken() from the server but getCsrfToken is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getCsrfToken\",\n);const getProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call getProviders() from the server but getProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"getProviders\",\n);const signIn = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call signIn() from the server but signIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"signIn\",\n);const signOut = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call signOut() from the server but signOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"signOut\",\n);const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\node_modules\\\\next-auth\\\\react.js\",\n\"SessionProvider\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/react.js\n");

/***/ })

};
;