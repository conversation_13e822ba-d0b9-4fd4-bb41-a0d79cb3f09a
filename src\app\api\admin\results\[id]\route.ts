import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const { id: resultId } = await params;

    // Get test result with candidate info
    const result = await db
      .select({
        id: testResults.id,
        candidateId: testResults.candidateId,
        listeningScore: testResults.listeningScore,
        listeningBandScore: testResults.listeningBandScore,
        readingScore: testResults.readingScore,
        readingBandScore: testResults.readingBandScore,
        writingTask1Score: testResults.writingTask1Score,
        writingTask2Score: testResults.writingTask2Score,
        writingBandScore: testResults.writingBandScore,
        speakingFluencyScore: testResults.speakingFluencyScore,
        speakingLexicalScore: testResults.speakingLexicalScore,
        speakingGrammarScore: testResults.speakingGrammarScore,
        speakingPronunciationScore: testResults.speakingPronunciationScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        certificateGenerated: testResults.certificateGenerated,
        createdAt: testResults.createdAt,
        updatedAt: testResults.updatedAt,
        candidate: {
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          passportNumber: candidates.passportNumber,
          nationality: candidates.nationality,
          testDate: candidates.testDate,
          testCenter: candidates.testCenter,
          photoUrl: candidates.photoUrl,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result[0]);

  } catch (error) {
    console.error('Error fetching admin test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const { id: resultId } = await params;
    const data = await request.json();

    // Check if result exists
    const existingResult = await db
      .select()
      .from(testResults)
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!existingResult.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: Record<string, unknown> = {
      updatedAt: new Date(),
    };

    // Add fields if they exist in the request
    if (data.listeningScore !== undefined) updateData.listeningScore = data.listeningScore ? parseInt(data.listeningScore) : null;
    if (data.listeningBandScore !== undefined) updateData.listeningBandScore = data.listeningBandScore ? parseFloat(data.listeningBandScore) : null;
    if (data.readingScore !== undefined) updateData.readingScore = data.readingScore ? parseInt(data.readingScore) : null;
    if (data.readingBandScore !== undefined) updateData.readingBandScore = data.readingBandScore ? parseFloat(data.readingBandScore) : null;
    if (data.writingTask1Score !== undefined) updateData.writingTask1Score = data.writingTask1Score ? parseFloat(data.writingTask1Score) : null;
    if (data.writingTask2Score !== undefined) updateData.writingTask2Score = data.writingTask2Score ? parseFloat(data.writingTask2Score) : null;
    if (data.writingBandScore !== undefined) updateData.writingBandScore = data.writingBandScore ? parseFloat(data.writingBandScore) : null;
    if (data.speakingFluencyScore !== undefined) updateData.speakingFluencyScore = data.speakingFluencyScore ? parseFloat(data.speakingFluencyScore) : null;
    if (data.speakingLexicalScore !== undefined) updateData.speakingLexicalScore = data.speakingLexicalScore ? parseFloat(data.speakingLexicalScore) : null;
    if (data.speakingGrammarScore !== undefined) updateData.speakingGrammarScore = data.speakingGrammarScore ? parseFloat(data.speakingGrammarScore) : null;
    if (data.speakingPronunciationScore !== undefined) updateData.speakingPronunciationScore = data.speakingPronunciationScore ? parseFloat(data.speakingPronunciationScore) : null;
    if (data.speakingBandScore !== undefined) updateData.speakingBandScore = data.speakingBandScore ? parseFloat(data.speakingBandScore) : null;
    if (data.overallBandScore !== undefined) updateData.overallBandScore = data.overallBandScore ? parseFloat(data.overallBandScore) : null;
    if (data.status !== undefined) updateData.status = data.status;

    // Update the test result
    await db
      .update(testResults)
      .set(updateData)
      .where(eq(testResults.id, resultId));

    return NextResponse.json({ message: 'Test result updated successfully' });

  } catch (error) {
    console.error('Error updating admin test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const { id: resultId } = await params;

    // Check if result exists
    const existingResult = await db
      .select()
      .from(testResults)
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!existingResult.length) {
      return NextResponse.json(
        { error: 'Test result not found' },
        { status: 404 }
      );
    }

    // Delete the test result
    await db
      .delete(testResults)
      .where(eq(testResults.id, resultId));

    return NextResponse.json({ message: 'Test result deleted successfully' });

  } catch (error) {
    console.error('Error deleting admin test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
